import { useState, useEffect } from 'react';
import { Globe, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

const GoogleTranslate = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('English');

  useEffect(() => {
    // Monitor for language changes
    const observer = new MutationObserver(() => {
      const selectElement = document.querySelector('select.goog-te-combo') as HTMLSelectElement;
      if (selectElement) {
        const selectedOption = selectElement.options[selectElement.selectedIndex];
        if (selectedOption && selectedOption.textContent) {
          setCurrentLanguage(selectedOption.textContent);
        }
      }
    });

    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['value']
    });

    return () => observer.disconnect();
  }, []);

  const handleToggle = () => {
    const newIsOpen = !isOpen;
    setIsOpen(newIsOpen);

    // Show/hide the Google Translate element
    const translateElement = document.getElementById('google_translate_element');
    if (translateElement) {
      if (newIsOpen) {
        translateElement.classList.add('active');
        translateElement.style.opacity = '1';
        translateElement.style.pointerEvents = 'auto';
      } else {
        translateElement.classList.remove('active');
        translateElement.style.opacity = '0';
        translateElement.style.pointerEvents = 'none';
      }
    }

    // Trigger the Google Translate dropdown
    setTimeout(() => {
      const selectElement = document.querySelector('select.goog-te-combo') as HTMLSelectElement;
      if (selectElement && newIsOpen) {
        selectElement.focus();
        selectElement.click();
      }
    }, 100);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('#google_translate_element') && !target.closest('[data-translate-button]')) {
        setIsOpen(false);
        const translateElement = document.getElementById('google_translate_element');
        if (translateElement) {
          translateElement.classList.remove('active');
          translateElement.style.opacity = '0';
          translateElement.style.pointerEvents = 'none';
        }
      }
    };

    if (isOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [isOpen]);

  return (
    <div className="relative">
      {/* Custom responsive button */}
      <Button
        variant="outline"
        size="sm"
        onClick={handleToggle}
        data-translate-button
        className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-1 sm:py-2 text-xs sm:text-sm border-green-200 hover:border-green-300 hover:bg-green-50"
      >
        <Globe className="h-3 w-3 sm:h-4 sm:w-4 text-green-600" />
        <span className="hidden sm:inline font-medium text-green-700">
          {currentLanguage}
        </span>
        <span className="sm:hidden font-medium text-green-700">
          {currentLanguage.slice(0, 2)}
        </span>
        <ChevronDown className="h-3 w-3 sm:h-4 sm:w-4 text-green-600" />
      </Button>

      {/* Hidden Google Translate element */}
      <div
        id="google_translate_element"
        className="absolute top-full right-0 mt-1 opacity-0 pointer-events-none overflow-hidden"
        style={{
          width: '1px',
          height: '1px',
          position: 'absolute',
          zIndex: -1
        }}
      />
    </div>
  );
};

export default GoogleTranslate;