import { useEffect } from 'react';

declare global {
  interface Window {
    google: any;
    googleTranslateElementInit: () => void;
  }
}

let scriptLoaded = false;
let translateInitialized = false;

export const useGoogleTranslate = () => {
  useEffect(() => {
    if (scriptLoaded) {
      return;
    }

    window.googleTranslateElementInit = () => {
      if (translateInitialized) {
        return;
      }

      setTimeout(() => {
        const element = document.getElementById('google_translate_element');
        if (element && window.google && window.google.translate && window.google.translate.TranslateElement) {
          const existingSelect = element.querySelector('select.goog-te-combo');
          if (existingSelect) {
            translateInitialized = true;
            return;
          }

          element.innerHTML = '';

          try {
            new window.google.translate.TranslateElement(
              {
                pageLanguage: 'en',
                includedLanguages: 'en,hi,mr',
                layout: window.google.translate.TranslateElement.InlineLayout.SIMPLE,
                autoDisplay: false,
                multilanguagePage: true,
              },
              'google_translate_element'
            );
            translateInitialized = true;

            setTimeout(() => {
              const selectElement = element.querySelector('select.goog-te-combo') as HTMLSelectElement;
              if (selectElement && selectElement.options) {
                const seenValues = new Set<string>();
                const seenTexts = new Set<string>();
                const options = Array.from(selectElement.options);

                options.forEach((option: HTMLOptionElement) => {
                  const value = option.value;
                  const text = option.textContent || option.innerText || '';

                  if ((seenValues.has(value) && value !== '') ||
                      (seenTexts.has(text) && text !== '' && text !== 'Select Language')) {
                    option.remove();
                  } else {
                    seenValues.add(value);
                    seenTexts.add(text);

                    if (text && text.length > 0) {
                      const halfLength = Math.floor(text.length / 2);
                      if (halfLength > 0 && text.substring(0, halfLength) === text.substring(halfLength)) {
                        option.textContent = text.substring(0, halfLength);
                      }
                    }
                  }
                });
              }
            }, 500);

          } catch (error) {
            console.warn('Google Translate initialization failed:', error);
          }
        }
      }, 200);
    };

    const script = document.createElement('script');
    script.src = 'https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';
    script.async = true;
    script.onerror = () => {
      console.error('Failed to load Google Translate script');
      scriptLoaded = false;
    };
    document.body.appendChild(script);

    const style = document.createElement('style');
    style.textContent = `
      .goog-te-banner-frame { display: none !important; }
      body { top: 0 !important; }
      .goog-te-gadget { color: transparent !important; background-color: transparent !important; border: none !important; }
      [id*="google_translate_element"] { display: block !important; }
      .skiptranslate iframe { visibility: hidden !important; display: none !important; }
      .goog-te-balloon-frame { display: none !important; }
      body.VIpgJd-ZVi9od-ORHb { top: 0 !important; margin-top: 0 !important; }
      .goog-te-gadget {
        font-family: 'Arial', sans-serif;
        margin: 0;
        height: auto;
      }
      .goog-te-gadget-simple {
        background-color: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 4px;
        display: flex !important;
        align-items: center;
        width: auto;
        min-width: 150px;
        max-width: 200px;
        height: auto;
        position: absolute;
        top: 100%;
        right: 0;
        z-index: 50;
        margin-top: 4px;
      }
      .goog-te-gadget-simple span {
        font-size: 15px;
      }
      .goog-te-gadget-icon {
        background-image: url("https://translate.googleapis.com/translate_static/img/te_ctrl3.gif");
        background-position: -65px 0px;
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }
      .VIpgJd-ZVi9od-xl07Ob-lTBxed {
        display: flex !important;
        align-items: center;
        justify-content: space-between;
        color: #333 !important;
        text-decoration: none !important;
        width: 100%;
      }
      #google_translate_element {
        display: block !important;
        position: absolute;
        top: 100%;
        right: 0;
        width: auto;
        height: auto;
        z-index: 50;
        opacity: 0;
        pointer-events: none;
        overflow: hidden;
      }
      #google_translate_element.active {
        opacity: 1;
        pointer-events: auto;
      }
      #\\:1\\.menuBody table {
        width: 100%;
      }
      .VIpgJd-ZVi9od-vH1Gmf-ibnC6b {
        display: block;
        padding: 12px;
        color: #333;
        text-decoration: none;
        font-size: 16px;
        transition: background-color 0.3s;
        white-space: nowrap;
      }
      .VIpgJd-ZVi9od-vH1Gmf-ibnC6b:hover {
        background-color: #f0f0f0;
        border-radius: 4px;
      }
      .VIpgJd-ZVi9od-vH1Gmf-ibnC6b-gk6SMd {
        display: block;
        padding: 12px;
        color: #333;
        text-decoration: none;
        font-size: 16px;
        background-color: #f0f0f0;
        border-radius: 4px;
      }
      .indicator {
        margin-right: 8px;
      }
      .text {
        font-size: 16px;
      }
      #\\:1\\.menuBody {
        box-sizing: border-box !important;
        width: auto !important;
        height: auto !important;
        min-width: 150px !important;
        max-width: 200px !important;
        background-color: white !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
        padding: 4px !important;
        position: fixed !important;
        z-index: 9999 !important;
      }

      /* Responsive adjustments */
      @media (max-width: 640px) {
        .goog-te-gadget-simple {
          min-width: 120px;
          max-width: 150px;
          font-size: 12px;
        }
        #\\:1\\.menuBody {
          min-width: 120px !important;
          max-width: 150px !important;
          font-size: 12px !important;
        }
        .VIpgJd-ZVi9od-vH1Gmf-ibnC6b {
          padding: 8px !important;
          font-size: 12px !important;
        }
      }
      #\\:1\\.menuBody table {
        width: 100% !important;
      }
    `;
    document.head.appendChild(style);
    scriptLoaded = true;

    const cleanupGoogleTranslateOptions = () => {
      const selectElement = document.querySelector('select.goog-te-combo') as HTMLSelectElement;
      if (selectElement && selectElement.options) {
        const seenValues = new Set<string>();
        const seenTexts = new Set<string>();
        const options = Array.from(selectElement.options);

        options.forEach((option: HTMLOptionElement) => {
          const value = option.value;
          const text = option.textContent || option.innerText || '';

          if ((seenValues.has(value) && value !== '') ||
              (seenTexts.has(text) && text !== '' && text !== 'Select Language')) {
            option.remove();
          } else {
            seenValues.add(value);
            seenTexts.add(text);

            if (text && text.length > 0) {
              const halfLength = Math.floor(text.length / 2);
              if (halfLength > 0 && text.substring(0, halfLength) === text.substring(halfLength)) {
                option.textContent = text.substring(0, halfLength);
              }
            }
          }
        });
      }
    };

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          const allSelects = document.querySelectorAll('select.goog-te-combo');
          if (allSelects.length > 1) {
            for (let i = 1; i < allSelects.length; i++) {
              const parent = allSelects[i].closest('.goog-te-gadget-simple');
              if (parent) {
                parent.remove();
              }
            }
          }

          setTimeout(cleanupGoogleTranslateOptions, 100);
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    const periodicCleanup = setInterval(() => {
      cleanupGoogleTranslateOptions();
    }, 3000);

    return () => {
      observer.disconnect();
      clearInterval(periodicCleanup);
    };

  }, []);
};
