import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Camera, Upload, Mic, Send, Bot, User, ImageIcon, Globe, RotateCw, X, ZoomIn } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import Navigation from '@/components/Navigation';
import GoogleTranslate from '@/components/googleTranslate';  // Add Google Translate import
import { useSpeechRecognition } from "react-speech-kit";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { ReactTransliterate } from "react-transliterate";
import { cn } from "@/lib/utils";
import { Dialog, DialogContent, DialogFooter, DialogTitle } from '@/components/ui/dialog';

// Extend Window interface for speech recognition
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

const languageData = {
  "en-US": { name: "English", nativeName: "English", country: "en-US" },
  "hi-IN": { name: "Hindi", nativeName: "हिन्दी", country: "hi-IN" },
  "mr-IN": { name: "Marathi", nativeName: "मराठी", country: "mr-IN" },
  "kn-IN": { name: "Kannada", nativeName: "ಕನ್ನಡ", country: "kn-IN" },
};

interface Message {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
  image?: string;
  images?: string[];
}

interface SelectedImage {
  file: File;
  preview: string;
  id: string;
}

const BotPage = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isListening, setIsListening] = useState(false);
  const [language, setLanguage] = useState('en-US');
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [selectedImages, setSelectedImages] = useState<SelectedImage[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [showCamera, setShowCamera] = useState(false);
  const { toast } = useToast();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [showCameraModal, setShowCameraModal] = useState(false);
  const [isCameraReady, setIsCameraReady] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [currentFacingMode, setCurrentFacingMode] = useState<'user' | 'environment'>('environment');
  const [availableCameras, setAvailableCameras] = useState<MediaDeviceInfo[]>([]);
  const [isSwitchingCamera, setIsSwitchingCamera] = useState(false);
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [viewerImageUrl, setViewerImageUrl] = useState<string>('');
 
  const { listen, listening, stop, supported } = useSpeechRecognition({
    onResult: (result: any) => {
      let cleanResult = '';
      if (typeof result === 'string') {
        cleanResult = result.trim();
      } else if (result && typeof result === 'object') {
        if (result.transcript) {
          cleanResult = String(result.transcript).trim();
        } else if (result.text) {
          cleanResult = String(result.text).trim();
        }
      }

      if (cleanResult && cleanResult !== '[object Object]' && cleanResult.length > 0) {
        setInputText(cleanResult);
      }
    },
    onEnd: () => {
      setIsListening(false);
    },
    onError: (error: any) => {
      console.error("Speech recognition error:", error);
      setIsListening(false);
    },
  });

  useEffect(() => {
    const generateSessionId = () => {
      return Date.now().toString(36) + Math.random().toString(36).substring(2);
    };
    const newSessionId = generateSessionId();
    setSessionId(newSessionId);
    setMessages([
      {
        id: '1',
        type: 'bot',
        content: '🌾 **Namaste! I am SugarBot, your AI farming assistant.**\n\nI can help you with:\n\n• **Disease Diagnosis** - Upload photos for instant crop analysis\n• **Farming Tips** - Get expert advice on cultivation practices\n• **Crop Management** - Receive guidance on fertilizers, irrigation, and pest control\n• **Harvest Planning** - Optimize your harvest timing for maximum yield\n• **Voice Support** - Ask questions using voice input\n• **Multilingual Support** - Communicate in English, Hindi, Marathi, or Kannada\n\nSelect your preferred language from the dropdown and start asking questions about your sugarcane farming!',
        timestamp: new Date()
      }
    ]);
  }, []);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isLoading]);

  // Get available cameras
  const getAvailableCameras = async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const cameras = devices.filter(device => device.kind === 'videoinput');
      setAvailableCameras(cameras);
      return cameras;
    } catch (error) {
      console.error('Error getting cameras:', error);
      return [];
    }
  };

  const handleSendMessage = async () => {
    // If there are selected images, use the image sending function
    if (selectedImages.length > 0) {
      await sendImagesWithMessage();
      return;
    }

    if (!inputText.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputText,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const messageToSend = inputText;
    // Clear input immediately
    setInputText('');
    setIsLoading(true);

    try {
      const requestBody = {
        query: messageToSend,
        sessionId,
        timestamp: Date.now(),
        language: language
      };

      const response = await fetch("https://n8n.onpointsoft.com/webhook/sugarcane", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API Error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      let responseContent = '';

      if (data.output) {
        responseContent = formatResponseContent(data.output);
      } else if (data.text) {
        responseContent = typeof data.text === 'string' ? data.text : String(data.text);
      } else {
        responseContent = 'No response received';
      }

      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: responseContent,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
    } catch (err) {
      console.error("API Error:", err);
      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: `I apologize, but I'm experiencing technical difficulties: ${err.message}. Please try again or contact support for assistance.`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
    } finally {
      setIsLoading(false);
    }
  };

  const formatResponseContent = (output: any): string => {
    if (typeof output === 'string') {
      return output;
    }

    let formattedContent = '';
    for (const [key, value] of Object.entries(output)) {
      formattedContent += `**${key}:**\n${value}\n\n`;
    }

    return formattedContent;
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const validFiles: File[] = [];

      // First, validate all files
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        if (!file.type.startsWith('image/')) {
          toast({
            title: "Invalid File",
            description: `${file.name} is not a valid image file`,
            variant: "destructive"
          });
          continue;
        }

        if (file.size > 10 * 1024 * 1024) {
          toast({
            title: "File Too Large",
            description: `${file.name} is larger than 10MB`,
            variant: "destructive"
          });
          continue;
        }

        validFiles.push(file);
      }

      // Process valid files
      if (validFiles.length > 0) {
        const newImages: SelectedImage[] = [];
        let processedCount = 0;

        validFiles.forEach((file, index) => {
          const reader = new FileReader();
          reader.onload = (e) => {
            const imageUrl = e.target?.result as string;
            const newImage: SelectedImage = {
              file: file,
              preview: imageUrl,
              id: `${Date.now()}_${index}_${Math.random().toString(36).substring(2)}`
            };
            newImages.push(newImage);
            processedCount++;

            // Update state when all valid files are processed
            if (processedCount === validFiles.length) {
              setSelectedImages(prev => [...prev, ...newImages]);
            }
          };
          reader.readAsDataURL(file);
        });
      }
    }
  };

  const removeImage = (imageId: string) => {
    setSelectedImages(prev => {
      const newImages = prev.filter(img => img.id !== imageId);
      // If no images left, reset file input
      if (newImages.length === 0 && fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      return newImages;
    });
  };

  const clearAllImages = () => {
    setSelectedImages([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const sendImagesWithMessage = async () => {
    if (selectedImages.length === 0 && !inputText.trim()) return;

    const messageContent = inputText.trim() || `I uploaded ${selectedImages.length} image(s) of my sugarcane crop for diagnosis.`;
    const imagePreviews = selectedImages.map(img => img.preview);

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: messageContent,
      images: imagePreviews,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    // Clear input text immediately when sending images
    setInputText('');
    setIsLoading(true);

    try {
      const formData = new FormData();

      // Add all images to FormData
      selectedImages.forEach((img, index) => {
        formData.append(`image${index}`, img.file);
      });

      formData.append('query', messageContent);
      formData.append('imageCount', selectedImages.length.toString());
      if (sessionId) {
        formData.append('sessionId', sessionId);
      }
      formData.append('timestamp', Date.now().toString());
      formData.append('language', language);

      const response = await fetch("https://n8n.onpointsoft.com/webhook/sugarcane", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API Error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      let responseContent = '';

      if (data.output) {
        responseContent = formatResponseContent(data.output);
      } else if (data.text) {
        responseContent = typeof data.text === 'string' ? data.text : String(data.text);
      } else {
        responseContent = 'No response received';
      }

      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: responseContent,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
    } catch (err) {
      console.error("Image Analysis Error:", err);
      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: `I apologize, but I'm having trouble analyzing your images: ${err.message}. Please try uploading the images again or contact support for assistance.`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
    } finally {
      setIsLoading(false);
      // Always clear images and reset file input after sending (success or error)
      setSelectedImages([]);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const startCamera = async (facingMode: 'user' | 'environment' = 'environment') => {
    try {
      // First check if getUserMedia is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("Camera API not supported in this browser");
      }
 
      // Show modal first
      setShowCameraModal(true);
      setIsCameraReady(false);
      
      // Get available cameras
      await getAvailableCameras();
 
      // Try simpler constraints first for better compatibility
      let constraints = {
        video: {
          width: { min: 320, ideal: 640, max: 1280 },
          height: { min: 240, ideal: 480, max: 720 },
          facingMode: facingMode
        },
        audio: false,
      };
 
      let stream: MediaStream;
      try {
        stream = await navigator.mediaDevices.getUserMedia(constraints);
      } catch (firstError) {
        console.warn("First attempt failed, trying basic constraints:", firstError);
        // Fallback to most basic constraints
        //@ts-ignore
        constraints = { video: { facingMode: facingMode }, audio: false };
        try {
          stream = await navigator.mediaDevices.getUserMedia(constraints);
        } catch (secondError) {
          // If facingMode fails, try without it
          //@ts-ignore
          constraints = { video: true, audio: false };
          stream = await navigator.mediaDevices.getUserMedia(constraints);
        }
      }
 
      if (videoRef.current && stream) {
        videoRef.current.srcObject = stream;
        setCurrentFacingMode(facingMode);
       
        // Set up event handlers before playing
        videoRef.current.onloadedmetadata = () => {
          console.log("Video metadata loaded");
          if (videoRef.current) {
            videoRef.current.play()
              .then(() => {
                console.log("Video playing successfully");
                setIsCameraReady(true);
                setIsSwitchingCamera(false);
              })
              .catch((playError) => {
                console.error("Video play error:", playError);
                setIsCameraReady(false);
                setIsSwitchingCamera(false);
              });
          }
        };
 
        videoRef.current.onloadstart = () => {
          console.log("Video load started");
        };
 
        videoRef.current.oncanplay = () => {
          console.log("Video can play");
          setIsCameraReady(true);
          setIsSwitchingCamera(false);
        };
 
        videoRef.current.onerror = (error) => {
          console.error("Video element error:", error);
          setIsCameraReady(false);
          setIsSwitchingCamera(false);
        };
 
        // Force load the video
        videoRef.current.load();
      }
 
    } catch (err) {
      console.error("Error starting camera:", err);
      setShowCameraModal(false);
      setIsSwitchingCamera(false);
     
      // More specific error handling
      let errorMessage = "Failed to access camera. ";
      if (err instanceof Error) {
        if (err.name === 'NotAllowedError') {
          errorMessage += "Please allow camera access in your browser settings and refresh the page.";
        } else if (err.name === 'NotFoundError') {
          errorMessage += "No camera device found on this device.";
        } else if (err.name === 'NotSupportedError' || err.name === 'NotReadableError') {
          errorMessage += "Camera is not accessible (may be in use by another application).";
        } else if (err.name === 'OverconstrainedError') {
          errorMessage += "Camera doesn't meet the required specifications.";
        } else {
          errorMessage += err.message;
        }
      }
     
      alert(errorMessage);
    }
  };

  const switchCamera = async () => {
    setIsSwitchingCamera(true);
    setIsCameraReady(false);
    
    // Stop current stream
    stopCameraStream();
    
    // Switch to the opposite facing mode
    const newFacingMode = currentFacingMode === 'user' ? 'environment' : 'user';
    
    // Start camera with new facing mode
    await startCamera(newFacingMode);
  };
 
  const capturePhoto = () => {
    if (videoRef.current && isCameraReady) {
      const canvas = document.createElement("canvas");
      const video = videoRef.current;

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      const ctx = canvas.getContext("2d");
      if (ctx) {
        // Draw the current video frame to canvas
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Convert canvas to image data URL
        const imageUrl = canvas.toDataURL("image/jpeg", 0.8);

        // Convert data URL to File object
        canvas.toBlob((blob) => {
          if (blob) {
            const file = new File([blob], `captured-photo-${Date.now()}.jpg`, {
              type: 'image/jpeg',
              lastModified: Date.now()
            });

            // Create SelectedImage object like uploaded images
            const newImage: SelectedImage = {
              file: file,
              preview: imageUrl,
              id: `captured_${Date.now()}_${Math.random().toString(36).substring(2)}`
            };

            // Add to selected images array
            setSelectedImages(prev => [...prev, newImage]);
          }
        }, 'image/jpeg', 0.8);

        // Close camera
        setInputText("");
        setShowCameraModal(false);
        stopCameraStream();

        toast({
          title: "Photo Captured",
          description: "Photo has been added to your message. Click Send to analyze it.",
        });
      }
    }
  };

  useEffect(() => {
    if (!showCameraModal) {
      stopCameraStream();
    }
  }, [showCameraModal]);

  // Add paste event listener
  useEffect(() => {
    document.addEventListener('paste', handlePaste);
    return () => {
      document.removeEventListener('paste', handlePaste);
    };
  }, []);
 
  const stopCameraStream = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach((track) => {
        track.stop();
      });
      videoRef.current.srcObject = null;
    }
    setIsCameraReady(false);
  };

  // Function to open image in full view
  const openImageViewer = (imageUrl: string) => {
    setViewerImageUrl(imageUrl);
    setShowImageViewer(true);
  };

  // Function to handle paste events
  const handlePaste = async (e: ClipboardEvent) => {
    const items = e.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile();
        if (file) {
          // Validate file size
          if (file.size > 10 * 1024 * 1024) {
            toast({
              title: "File Too Large",
              description: "Pasted image is larger than 10MB",
              variant: "destructive"
            });
            continue;
          }

          // Create preview URL
          const reader = new FileReader();
          reader.onload = (e) => {
            const imageUrl = e.target?.result as string;
            const newImage: SelectedImage = {
              file: file,
              preview: imageUrl,
              id: `pasted_${Date.now()}_${Math.random().toString(36).substring(2)}`
            };

            // Add to selected images array
            setSelectedImages(prev => [...prev, newImage]);

            toast({
              title: "Image Pasted",
              description: "Image has been added from clipboard. Click Send to analyze it.",
            });
          };
          reader.readAsDataURL(file);
        }
      }
    }
  };



  const toggleMic = () => {
    if (!supported) {
      toast({
        title: "Speech Recognition Not Supported",
        description: "Speech recognition is not supported in your browser. Please use Chrome, Edge, or Safari.",
        variant: "destructive"
      });
      return;
    }

    if (listening || isListening) {
      stop();
      setIsListening(false);
    } else {
      setInputText("");
      setIsListening(true);
      try {
        listen({
          lang: language,
          continuous: true,
          interimResults: true,
        });
      } catch (error) {
        console.error("Error starting speech recognition:", error);
        setIsListening(false);
        toast({
          title: "Speech Recognition Error",
          description: "Failed to start speech recognition. Please check your microphone permissions.",
          variant: "destructive"
        });
      }
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-2 sm:px-4 pt-2 sm:pt-4 pb-4 sm:pb-8">
        <div className="max-w-4xl mx-auto">
          {/* Header with title and Google Translate */}
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-4 sm:mb-8 px-2 gap-3 sm:gap-4">
            <div className="text-center sm:text-left flex-1">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground mb-2 sm:mb-4">
                SugarBot - Your AI Farming Assistant
              </h1>
              <p className="text-sm sm:text-base lg:text-lg text-muted-foreground">
                Upload photos, ask questions, or speak in your local language for instant farming guidance
              </p>
            </div>
            <div className="flex justify-center sm:justify-end sm:flex-shrink-0">
              <GoogleTranslate />
            </div>
          </div>

          <Card className="shadow-elevated mx-2 sm:mx-0">
            <CardHeader className="bg-gradient-primary rounded-t-lg p-3 sm:p-6">
              <CardTitle className="text-hero-fg flex items-center gap-2 text-sm sm:text-base">
                <Bot className="h-4 w-4 sm:h-6 sm:w-6" />
                Chat with SugarBot
              </CardTitle>
            </CardHeader>

            <CardContent className="p-0">
              {/* Messages Area */}
              <div className="h-64 sm:h-80 lg:h-96 overflow-y-auto p-3 sm:p-6 space-y-3 sm:space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-2 sm:gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`flex gap-2 sm:gap-3 max-w-[85%] sm:max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                      <div className={`p-1.5 sm:p-2 h-6 w-6 sm:h-8 sm:w-8 rounded-full flex-shrink-0 ${message.type === 'user' ? 'bg-primary' : 'bg-earth-green'}`}>
                        {message.type === 'user' ? (
                          <User className="h-3 w-3 sm:h-4 sm:w-4 text-primary-foreground" />
                        ) : (
                          <Bot className="h-3 w-3 sm:h-4 sm:w-4 text-primary-foreground" />
                        )}
                      </div>

                      <div className={`p-3 sm:p-4 rounded-lg ${
                        message.type === 'user'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted text-foreground'
                      }`}>
                        {message.image && (
                          <div className="relative group cursor-pointer" onClick={() => openImageViewer(message.image!)}>
                            <img
                              src={message.image}
                              alt="Uploaded crop"
                              className="max-w-[200px] sm:max-w-xs rounded-lg mb-2 w-full hover:opacity-90 transition-opacity"
                            />
                            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black bg-opacity-30 rounded-lg">
                              <ZoomIn className="h-6 w-6 text-white" />
                            </div>
                          </div>
                        )}
                        {message.images && message.images.length > 0 && (
                          <div className="mb-2">
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 max-w-[200px] sm:max-w-xs">
                              {message.images.map((imageUrl, index) => (
                                <div
                                  key={index}
                                  className="relative group cursor-pointer"
                                  onClick={() => openImageViewer(imageUrl)}
                                >
                                  <img
                                    src={imageUrl}
                                    alt={`Uploaded crop ${index + 1}`}
                                    className="w-full h-16 sm:h-24 object-cover rounded-lg border hover:opacity-90 transition-opacity"
                                  />
                                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black bg-opacity-30 rounded-lg">
                                    <ZoomIn className="h-4 w-4 text-white" />
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        <div className="text-sm">
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            components={{
                              p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                              ul: ({ children }) => <ul className="list-disc list-inside mb-2">{children}</ul>,
                              ol: ({ children }) => <ol className="list-decimal list-inside mb-2">{children}</ol>,
                              li: ({ children }) => <li className="mb-1">{children}</li>,
                              h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                              h2: ({ children }) => <h2 className="text-base font-semibold mb-2">{children}</h2>,
                              h3: ({ children }) => <h3 className="text-sm font-medium mb-1">{children}</h3>,
                              strong: ({ children }) => <strong className="font-bold">{children}</strong>,
                              code: ({ children, className }) => {
                                const isInline = !className;
                                return isInline ? (
                                  <code className="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono">{children}</code>
                                ) : (
                                  <code className="block bg-gray-100 p-2 rounded text-xs font-mono overflow-x-auto">{children}</code>
                                );
                              },
                            }}
                          >
                            {typeof message.content === 'string' ? message.content : String(message.content || '')}
                          </ReactMarkdown>
                        </div>
                        <p className={`text-xs mt-2 ${
                          message.type === 'user' ? 'text-primary-foreground/70' : 'text-muted-foreground'
                        }`}>
                          {message.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}

                {isLoading && (
                  <div className="flex gap-2 sm:gap-3 justify-start">
                    <div className="p-1.5 sm:p-2 rounded-full bg-earth-green h-6 w-6 sm:h-8 sm:w-8 flex-shrink-0">
                      <Bot className="h-3 w-3 sm:h-4 sm:w-4 text-primary-foreground" />
                    </div>
                    <div className="p-3 sm:p-4 rounded-lg bg-muted">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce delay-100"></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce delay-200"></div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* Camera View */}
              {showCamera && (
                <div className="p-6 border-t bg-background">
                  <div className="text-center">
                    <div className="relative inline-block mb-4">
                      <video 
                        ref={videoRef} 
                        autoPlay 
                        playsInline
                        muted
                        className="w-80 h-60 rounded-lg border-2 border-primary bg-black object-cover"
                      />
                      <div className="absolute top-2 right-2 bg-primary text-primary-foreground px-2 py-1 rounded text-xs">
                        Camera Active
                      </div>
                    </div>
                    <div className="flex gap-4 justify-center">
                      <Button 
                        onClick={capturePhoto} 
                        variant="hero" 
                        size="lg"
                        className="px-6 py-3"
                      >
                        <Camera className="h-5 w-5 mr-2" />
                        Capture Photo
                      </Button>
                      <Button 
                        onClick={() => {
                          const stream = videoRef.current?.srcObject as MediaStream;
                          stream?.getTracks().forEach(track => track.stop());
                          setShowCamera(false);
                        }}
                        variant="outline"
                        size="lg"
                        className="px-6 py-3"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Selected Images Preview */}
              {selectedImages.length > 0 && (
                <div className="p-3 sm:p-4 border-t bg-muted/30">
                  <div className="flex items-center justify-between mb-2 sm:mb-3">
                    <div className="flex items-center gap-2">
                      <ImageIcon className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
                      <span className="text-xs sm:text-sm font-medium text-muted-foreground">
                        Selected Images ({selectedImages.length})
                      </span>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={clearAllImages}
                      className="text-xs h-6 px-2"
                    >
                      Clear All
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 sm:gap-3">
                    {selectedImages.map((image) => (
                      <div key={image.id} className="relative group">
                        <img
                          src={image.preview}
                          alt="Selected crop"
                          className="w-16 h-16 sm:w-20 sm:h-20 object-cover rounded-lg border border-border cursor-pointer hover:opacity-90 transition-opacity"
                          onClick={() => openImageViewer(image.preview)}
                        />
                        <Button
                          size="sm"
                          variant="destructive"
                          className="absolute -top-1 -right-1 sm:-top-2 sm:-right-2 h-5 w-5 sm:h-6 sm:w-6 p-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity text-xs z-10"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeImage(image.id);
                          }}
                        >
                          ×
                        </Button>
                        <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-1 rounded-b-lg truncate pointer-events-none">
                          {image.file.name}
                        </div>
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black bg-opacity-20 rounded-lg pointer-events-none">
                          <ZoomIn className="h-3 w-3 text-white" />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Input Area */}
              <div className="p-3 sm:p-6 border-t">
                <div className="flex flex-wrap gap-2 mb-3 sm:mb-4">
                  <div className="relative">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-green-600 hover:bg-green-50 text-xs sm:text-sm px-2 sm:px-3"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setShowLanguageDropdown(!showLanguageDropdown);
                      }}
                    >
                      <Globe className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                      <span className="font-medium hidden sm:inline">
                        {languageData[language]?.nativeName || "Language"}
                      </span>
                      <span className="font-medium sm:hidden">
                        {languageData[language]?.nativeName?.slice(0, 3) || "Lang"}
                      </span>
                    </Button>

                    {showLanguageDropdown && (
                      <div className="absolute left-0 bottom-full mb-2 bg-white border border-green-100 rounded-lg shadow-lg py-1 min-w-[120px] max-w-[200px] z-50">
                        {Object.entries(languageData).map(([code, lang]) => (
                          <div
                            key={code}
                            className="px-2 sm:px-3 py-1.5 cursor-pointer hover:bg-green-50 text-xs sm:text-sm text-gray-700"
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              setLanguage(code);
                              setShowLanguageDropdown(false);
                            }}
                          >
                            {lang.nativeName}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    variant="outline"
                    size="sm"
                    className="px-2 sm:px-3"
                  >
                    <Upload className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="hidden sm:inline ml-1">Upload</span>
                  </Button>

                  <Button
                    onClick={() => startCamera()}
                    variant="outline"
                    size="sm"
                    className="px-2 sm:px-3"
                  >
                    <Camera className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="hidden sm:inline ml-1">Camera</span>
                  </Button>

                  <Button
                    onClick={toggleMic}
                    variant={(listening || isListening) ? "destructive" : "outline"}
                    size="sm"
                    disabled={!supported}
                    className="px-2 sm:px-3"
                  >
                    <Mic className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="hidden sm:inline ml-1">
                      {(listening || isListening) ? 'Listening...' : 'Voice'}
                    </span>
                  </Button>
                </div>

                <div className="flex gap-2">
                  {language === "en-US" ? (
                    <Textarea
                      value={inputText}
                      onChange={(e) => setInputText(e.target.value)}
                      placeholder={
                        listening || isListening
                          ? "🎤 Listening for farming questions..."
                          : "Ask about your sugarcane crops, diseases, fertilizers, or farming tips... (You can also paste images with Ctrl+V)"
                      }
                      className={cn(
                        "min-h-[50px] sm:min-h-[60px] text-sm sm:text-base",
                        (listening || isListening) &&
                          "border-red-400 ring-4 ring-red-100 bg-red-50"
                      )}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSendMessage();
                        }
                      }}
                    />
                  ) : (
                    <div className="flex-1">
                      <ReactTransliterate
                        lang={language.split("-")[0] as any}
                        value={inputText}
                        onChangeText={(text) => setInputText(text)}

                        placeholder={
                          listening || isListening
                            ? "🎤 शेती प्रश्नांसाठी ऐकत आहे..."
                            : "आपल्या ऊस पिकांबद्दल, रोगांबद्दल, खतांबद्दल किंवा शेती टिप्सबद्दल विचारा... (Ctrl+V ने चित्र पेस्ट करू शकता)"
                        }
                        renderComponent={(props) => (
                          <textarea
                            {...props}
                            onKeyDown={handleKeyPress}
                            className={cn(
                              "w-full px-3 py-2 border border-green-200 rounded-md focus:border-green-400 focus:ring-green-400 focus:outline-none min-h-[50px] sm:min-h-[60px] resize-none text-sm sm:text-base",
                              (listening || isListening) &&
                                "border-red-400 ring-4 ring-red-100 bg-red-50"
                            )}
                            style={{
                              width: "100%",
                              minWidth: "100%",
                            }}
                          />
                        )}
                      />
                    </div>
                  )}
                  <Button
                    onClick={handleSendMessage}
                    disabled={!inputText.trim() && selectedImages.length === 0}
                    variant="hero"
                    size="lg"
                    className="px-3 sm:px-8"
                  >
                    <Send className="h-3 w-3 sm:h-4 sm:w-4" />
                  </Button>
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mt-4 sm:mt-8 mx-2 sm:mx-0">
            <Card className="text-center p-3 sm:p-4 hover:shadow-soft transition-all duration-300">
              <ImageIcon className="h-6 w-6 sm:h-8 sm:w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-foreground mb-1 text-sm sm:text-base">Photo Diagnosis</h3>
              <p className="text-xs sm:text-sm text-muted-foreground">Upload crop images for instant disease identification</p>
            </Card>

            <Card className="text-center p-3 sm:p-4 hover:shadow-soft transition-all duration-300">
              <Mic className="h-6 w-6 sm:h-8 sm:w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-foreground mb-1 text-sm sm:text-base">Voice Support</h3>
              <p className="text-xs sm:text-sm text-muted-foreground">Speak in Kannada, Hindi, or English</p>
            </Card>

            <Card className="text-center p-3 sm:p-4 hover:shadow-soft transition-all duration-300 sm:col-span-2 lg:col-span-1">
              <Bot className="h-6 w-6 sm:h-8 sm:w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-foreground mb-1 text-sm sm:text-base">24/7 Available</h3>
              <p className="text-xs sm:text-sm text-muted-foreground">Get farming guidance anytime, anywhere</p>
            </Card>
          </div>
        </div>
      </div>

      {/* Image Viewer Modal */}
      <Dialog open={showImageViewer} onOpenChange={setShowImageViewer}>
        <DialogContent className="max-w-4xl mx-4 p-0">
          <DialogTitle className="sr-only">Image Viewer</DialogTitle>
          <div className="relative">
            <img
              src={viewerImageUrl}
              alt="Full size view"
              className="w-full h-auto max-h-[80vh] object-contain rounded-lg"
            />
            <Button
              onClick={() => setShowImageViewer(false)}
              variant="outline"
              size="sm"
              className="absolute top-2 right-2 bg-white/90 hover:bg-white"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Camera Modal */}
      <Dialog open={showCameraModal} onOpenChange={setShowCameraModal}>
        <DialogContent className="max-w-sm sm:max-w-lg mx-4">
          <DialogTitle className="text-sm sm:text-base">Capture Photo</DialogTitle>
          <div className="flex flex-col items-center space-y-3 sm:space-y-4">
            <div className="relative w-full">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="w-full rounded-lg border bg-gray-900"
                style={{ maxHeight: '300px', minHeight: '200px' }}
              />
              {(!isCameraReady || isSwitchingCamera) && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-90 rounded-lg">
                  <div className="text-white text-center">
                    <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-white mx-auto mb-2"></div>
                    <p className="text-xs sm:text-sm">
                      {isSwitchingCamera ? "Switching camera..." : "Initializing camera..."}
                    </p>
                    <p className="text-xs mt-1 opacity-75">
                      {isSwitchingCamera ? "Please wait" : "Please allow camera access"}
                    </p>
                  </div>
                </div>
              )}
              {isCameraReady && !isSwitchingCamera && (
                <div className="absolute top-1 sm:top-2 right-1 sm:right-2 bg-green-600 text-white px-1.5 sm:px-2 py-0.5 sm:py-1 rounded text-xs">
                  📹 Ready
                </div>
              )}
              {isCameraReady && !isSwitchingCamera && (
                <div className="absolute top-1 sm:top-2 left-1 sm:left-2 bg-blue-600 text-white px-1.5 sm:px-2 py-0.5 sm:py-1 rounded text-xs">
                  {currentFacingMode === 'user' ? '🤳 Front' : '📷 Back'}
                </div>
              )}
            </div>
            <DialogFooter className="flex flex-col sm:flex-row justify-center gap-2 sm:gap-4 w-full">
              <Button
                onClick={capturePhoto}
                disabled={!isCameraReady || isSwitchingCamera}
                className="bg-green-600 hover:bg-green-700 text-white w-full sm:w-auto"
                size="sm"
              >
                <Camera className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                <span className="text-xs sm:text-sm">Capture Photo</span>
              </Button>
              <Button
                onClick={switchCamera}
                disabled={!isCameraReady || isSwitchingCamera || availableCameras.length < 2}
                variant="outline"
                className="border-blue-500 text-blue-500 hover:bg-blue-50 w-full sm:w-auto"
                size="sm"
              >
                <RotateCw className={`h-3 w-3 sm:h-4 sm:w-4 mr-2 ${isSwitchingCamera ? 'animate-spin' : ''}`} />
                <span className="text-xs sm:text-sm">Switch</span>
              </Button>
              <Button
                onClick={() => {
                  setShowCameraModal(false);
                  stopCameraStream();
                }}
                variant="outline"
                className="w-full sm:w-auto"
                size="sm"
              >
                <span className="text-xs sm:text-sm">Cancel</span>
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>
 
      {/* Hidden canvas for image processing */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
};

export default BotPage;

